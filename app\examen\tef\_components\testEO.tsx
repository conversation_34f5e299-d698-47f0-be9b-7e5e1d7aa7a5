'use client';
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

import Image from 'next/image';
import Dialog from '@/components/Dialog';
import logo2 from '@/public/logo2.jpg';
import type { Session } from 'next-auth';
import { EndTextBtn } from '../../_components/button';
import { toast as sonner, toast } from 'sonner';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { saveTestEOUseCase } from '../actions';
import useCredits from '@/hooks/use-credits';
import { useEOStore } from '@/context/ee-provider';
import { convertFromBase64, convertToBase64 } from '../../utils';
import { Button } from '@/components/ui/button';
import { v4 as uuid } from 'uuid';
import { initDB, useIndexedDB } from 'react-indexed-db-hook';
import { deleteR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TEFDBConfig } from '@/config/db';
import TEFTaskEO2, { TEFTaskEO2Ref } from './t2';
import TEFTaskEO1 from './t1';
import { useSession } from 'next-auth/react';
import logger from '@/lib/logger';
import InitialCorrectionModeSelector from '../../_components/initial-correction-mode-selector';
import FloatingCorrectionMode from '../../_components/floating-correction-mode';

async function retryUpload(file: File, token: string) {
  const formData = new FormData();
  formData.append('files', file);
  let attempt = 0;
  while (attempt < 3) {
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/user-eofile/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Accept: '*',
            Authorization: `Bearer ${token}`,
          },
        },
      );
      const name = data.file.filename as string;
      logger.log(`%cUPLOAD SUCESSFUL :${name}`);
      return name;
    } catch (error) {
      // console.log(error);

      logger.error(`Upload attempt ${attempt + 1} failed with error: ${error}`);
      if (attempt == 3) {
        throw new Error(`Impossile d'uploader le fichier`);
      }
      attempt++;
    }
  }
}
const uploadSound = async ({
  f1,
  f2,
  token,
}: {
  f1: File;
  f2: File;
  token: string;
}) => {
  let taskUrl1: string | undefined;
  let taskUrl2: string | undefined;

  taskUrl1 = await retryUpload(f1, token);
  taskUrl2 = await retryUpload(f2, token);
  return taskUrl1 && taskUrl2 ? { taskUrl1, taskUrl2 } : null;
};

initDB(TEFDBConfig);

export default function TestEOTEF({
  session,
  serie,
}: {
  session: Session;
  serie: SerieTEF;
}) {
  const { update } = useSession();
  const db = useIndexedDB('testeo');
  const current = useEOStore((state) => state.current);
  const reset = useEOStore((state) => state.reset);
  const fileOne = useEOStore((state) => state.fileOne);
  const fileTwo = useEOStore((state) => state.fileTwo);
  const prevSetFileOne = useEOStore((state) => state.prevSetFileOne);
  const prevSetFileTwo = useEOStore((state) => state.prevSetFileTwo);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [showInitialSelector, setShowInitialSelector] = useState(true);
  const [correctionMode, setCorrectionMode] = useState<string>('');
  const btnref = useRef<HTMLButtonElement>(null);
  const stopBtn = useRef<HTMLButtonElement>(null);
  const hasChecked = useRef(false);
  const task2 = useRef<TEFTaskEO2Ref>(null);
  const taskOne = serie.eoQuestions[0].sections.find((t) => t.numero == 41)!;
  const taskTwo = serie.eoQuestions[0].sections.find((t) => t.numero == 42)!;
  const { checkCredit } = useCredits();
  const router = useRouter();
  const SaveToLocalStorage = async ({
    liblle,
    userId,
    fileOne,
    fileTwo,
  }: {
    liblle: string;
    userId: string;
    fileOne: File;
    fileTwo: File;
  }) => {
    const storageKey = `TEF_EO_RES_${liblle}_${userId}`;
    const f1Base64 = await convertToBase64(fileOne);
    const f2Base64 = await convertToBase64(fileTwo);
    const payload = {
      fileOne: f1Base64,
      fileTwo: f2Base64,
    };
    const data = {
      key: storageKey,
      payload,
    };
    await db.add(data);
  };
  const handleInitialModeSelection = (mode: string) => {
    setCorrectionMode(mode);
    setShowInitialSelector(false);
  };

  const handleModeChange = (mode: string) => {
    setCorrectionMode(mode);
  };

  const sendTest = async () => {
    const data = await uploadSound({
      f1: fileOne!,
      f2: fileTwo!,
      token: session.user.accessToken as string,
    });
    if (!data) throw new Error("Impossible d'uploader le fichier");
    await saveTestEOUseCase({
      payload: data,
      serie: serie._id,
      correctionType: correctionMode,
    });
  };

  const submitTest = async () => {
    setIsSubmitting(true);
    try {
      await SaveToLocalStorage({
        liblle: serie.libelle,
        userId: session.user._id,
        fileOne: fileOne!,
        fileTwo: fileTwo!,
      });

      if (!(await checkCredit('TEF', 'EO'))) {
        setIsSubmitting(false);
        return;
      }

      sonner.promise(
        async () => {
          return await sendTest();
        },
        {
          loading: 'Envoie du test en cours...',
          success: () => {
            update({
              remains: {
                remainTEF: {
                  balance_ee: session?.user.remains?.remainTEF?.balance_ee,
                  balance_eo: session?.user.remains?.remainTEF?.balance_eo! - 1,
                  remain_day: session?.user.remains?.remainTEF?.remain_day,
                },
                remainTCF: session?.user.remains?.remainTCF,
              },
            });
            const storageKey = `TEF_EO_RES_${serie.libelle}_${session.user._id}`;
            const hasShownKey = `${storageKey}_shown`;
            deleteRecordByKey('TEF-EO', 'testeo', storageKey).then(() => {
              localStorage.removeItem(hasShownKey);
              router.back();
            });
            return "Vos productions audio ont bien été soumises. Vous serrez informé(e) lorsqu'un resultat sera disponible";
          },
          error: (error) => {
            setIsSubmitting(false);
            return error.message === 'Failed to fetch'
              ? 'Verifier votre connexion'
              : error.message;
          },
          closeButton: true,
          duration: 1000 * 20,
        },
      );
    } catch (error) {
      setIsSubmitting(false);
      sonner.error("Une erreur s'est produite lors de l'envoi du test", {
        description: 'Veuillez réessayer plus tard',
        duration: 1000 * 60 * 5,
        closeButton: true,
      });
    }
  };

  const onExpire = async () => {
    if (fileOne !== null && fileTwo !== null) {
      await submitTest();
    } else {
      reset();
      router.push(`/examen/tef/${serie.libelle}`);
      sonner.warning(
        'Veuillez enregistrer vos 2 productions avant de soumettre',
        {
          description: 'Vous pourrez terminer votre test plus tard',
          duration: 1000 * 20,
          closeButton: true,
        },
      );
    }
  };

  const loadPrevTest = async () => {
    const storageKey = `TEF_EO_RES_${serie.libelle}_${session.user._id}`;
    const hasShownKey = `${storageKey}_shown`;
    const data = await db.getByIndex('key', storageKey);
    const payload = data ? data.payload : undefined;

    const f1 = await convertFromBase64(payload.fileOne as string, uuid());
    const f2 = await convertFromBase64(payload.fileTwo as string, uuid());

    prevSetFileOne(f1);
    prevSetFileTwo(f2);

    await deleteRecordByKey('TEF-EO', 'testeo', storageKey);
    localStorage.removeItem(hasShownKey);
  };

  useEffect(() => {
    if (!serie?.libelle || !session?.user?._id || isShow || hasChecked.current)
      return;

    const storageKey = `TEF_EO_RES_${serie.libelle}_${session.user._id}`;
    const hasShownKey = `${storageKey}_shown`;

    if (localStorage.getItem(hasShownKey)) return;
    hasChecked.current = true;
    db.getByIndex('key', storageKey).then((data: any) => {
      const payload = data ? data.payload : undefined;

      if (payload) {
        setIsShow(true);
        localStorage.setItem(hasShownKey, 'true');

        toast.custom(
          (id) => (
            <EOPersitence
              id={id}
              message="Un de vos tests précédents a été détecté. Voulez-vous le charger ? Cliquer sur Non effacera le test."
              handleYes={loadPrevTest}
              handleNo={async () => {
                await deleteRecordByKey('TEF-EO', 'testeo', storageKey);
                localStorage.removeItem(hasShownKey);
              }}
            />
          ),
          {
            position: 'bottom-left',
            closeButton: true,
            duration: 1000 * 60 * 5,
          },
        );
      }
    });
  }, [serie, session, isShow]);

  return (
    <div className="relative flex w-full flex-col gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex w-full flex-row items-center justify-around border-b bg-white p-2 md:justify-center">
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TEF: Objectif-Canada || Série {serie.libelle} || Production orale.
          </p>
        </div>
        <EndTextBtn
          onClick={() => {
            onExpire();
          }}
        >
          <button
            disabled={isSubmitting || !fileOne || !fileTwo}
            title="stop"
            ref={stopBtn}
            className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1 disabled:cursor-not-allowed disabled:bg-red-300/80 md:p-3"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
      </div>

      <div className="relative mx-auto mt-20 w-full flex-1 space-y-2 md:mt-14">
        <div className="flex items-center gap-3 p-2 text-xs md:text-sm">
          <span
            onClick={() => {
              if (current !== 1) btnref.current?.click();
            }}
            className={cn('rounded-md px-3 py-2 text-white', {
              'bg-blue-700 font-semibold': current == 1,
              'bg-blue-500/60 font-normal': !(current == 1),
            })}
          >
            Section A
          </span>
          <span
            onClick={() => {
              if (current !== 2) btnref.current?.click();
            }}
            className={cn('rounded-md px-3 py-2 text-white', {
              'bg-blue-700 font-semibold': current == 2,
              'bg-blue-500/60 font-normal': !(current == 2),
            })}
          >
            Section B
          </span>
        </div>
        <Image
          src={logo2}
          width={1277}
          height={1280}
          priority
          className="absolute right-2 top-2 hidden h-40 w-40 md:block"
          alt="logo"
        />
        {current == 1 ? <TEFTaskEO1 stopBtn={stopBtn} task={taskOne} /> : null}
        {current == 2 ? (
          <TEFTaskEO2 stopBtn={stopBtn} task={taskTwo} ref={task2} />
        ) : null}

        <Dialog
          description="Vous ne pouvez pas passer d'une tâche à l'autre lors d'un test d'Expression orale"
          title="Info"
          ref={btnref}
          onClick={() => {}}
        />
      </div>

      {/* Sélecteur initial de mode de correction */}
      <InitialCorrectionModeSelector
        isOpen={showInitialSelector}
        onConfirm={handleInitialModeSelection}
        session={session}
        testType="EO"
      />

      {/* Popup flottant pour changer le mode de correction */}
      {!showInitialSelector && correctionMode && (
        <FloatingCorrectionMode
          session={session}
          currentMode={correctionMode}
          onModeChange={handleModeChange}
        />
      )}
    </div>
  );
}

function EOPersitence({
  message,
  id,
  handleYes,
  handleNo,
}: {
  message: string;
  id: string | number;
  handleYes: () => Promise<void>;
  handleNo: () => void;
}) {
  const router = useRouter();
  return (
    <div className="flex flex-col gap-2 rounded-sm border bg-white p-3">
      <span className="p-0 text-center text-xl font-semibold text-red-500">
        Information !!!
      </span>
      <span className="max-w-[40ch] p-0 text-center text-base font-bold">
        {message}
      </span>

      <div className="flex items-center justify-between">
        <Button
          variant={'destructive'}
          className="uppercase"
          onClick={() => {
            handleNo();
            toast.dismiss(id);
            router.back();
          }}
        >
          Non
        </Button>
        <Button
          onClick={async () => {
            await handleYes();
            toast.dismiss(id);
          }}
          className="animate-buttonheartbeat bg-green-500 uppercase text-white hover:bg-green-300"
        >
          Oui
        </Button>
      </div>
    </div>
  );
}
