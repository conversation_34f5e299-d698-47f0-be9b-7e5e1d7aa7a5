'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { isProfile } from '@/lib/utils';
import type { Session } from 'next-auth';

interface InitialCorrectionModeSelectorProps {
  isOpen: boolean;
  onConfirm: (correctionType: string) => void;
  session: Session;
  testType: string; // 'EE' ou 'EO'
}

export default function InitialCorrectionModeSelector({
  isOpen,
  onConfirm,
  session,
  testType,
}: InitialCorrectionModeSelectorProps) {
  const [selectedMode, setSelectedMode] = useState<string>('');

  const isProfileUser = isProfile(session?.user?.email);

  // Définir les options selon le type d'utilisateur
  const getCorrectionOptions = () => {
    if (isProfileUser) {
      // Les profils n'ont que l'option humain
      return [
        {
          value: 'humain',
          label: 'Humain',
          description: 'Correction par un correcteur humain (plus précise)',
        },
      ];
    } else {
      return [
        {
          value: 'ia_renforce',
          label: 'Humain',
          description: 'Correction par un correcteur humain (plus précise)',
        },
        {
          value: 'ia',
          label: 'IA',
          description: 'Correction instantanée par IA',
        },
      ];
    }
  };

  const options = getCorrectionOptions();

  // Pour les profils, sélectionner automatiquement "humain"
  React.useEffect(() => {
    if (isProfileUser && isOpen) {
      setSelectedMode('humain');
    }
  }, [isProfileUser, isOpen]);

  const handleConfirm = () => {
    if (selectedMode) {
      onConfirm(selectedMode);
    }
  };

  const getTestTypeLabel = () => {
    return testType === 'EE' ? 'Expression Écrite' : 'Expression Orale';
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent
        className="sm:max-w-md"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>Mode de correction - {getTestTypeLabel()}</DialogTitle>
          <DialogDescription>
            Choisissez le type de correction que vous souhaitez pour votre test.
            Vous pourrez modifier ce choix pendant le test.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <RadioGroup value={selectedMode} onValueChange={setSelectedMode}>
            {options.map((option) => (
              <div
                key={option.value}
                className="flex items-start space-x-3 space-y-0"
              >
                <RadioGroupItem
                  value={option.value}
                  id={option.value}
                  className="mt-1"
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor={option.value}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="flex justify-end pt-4">
          <Button
            onClick={handleConfirm}
            disabled={!selectedMode}
            className="w-full"
          >
            Commencer le test
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
