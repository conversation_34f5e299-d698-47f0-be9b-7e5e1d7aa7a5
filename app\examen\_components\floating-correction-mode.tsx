'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Settings, Check } from 'lucide-react';
import { isProfile } from '@/lib/utils';
import type { Session } from 'next-auth';

interface FloatingCorrectionModeProps {
  session: Session;
  currentMode: string;
  onModeChange: (mode: string) => void;
}

export default function FloatingCorrectionMode({
  session,
  currentMode,
  onModeChange,
}: FloatingCorrectionModeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMode, setSelectedMode] = useState(currentMode);

  const isProfileUser = isProfile(session?.user?.email);

  // Les profils n'ont que l'option humain
  if (isProfileUser) {
    return null; // Pas de popup pour les profils
  }

  const options = [
    {
      value: 'ia_renforce',
      label: 'Humain',
      description: 'Correction par un correcteur humain (plus précise)',
    },
    {
      value: 'ia',
      label: 'IA',
      description: 'Correction instantanée par IA',
    },
  ];

  const getCurrentModeLabel = () => {
    const option = options.find((opt) => opt.value === currentMode);
    return option?.label || 'Non défini';
  };

  const handleConfirm = () => {
    onModeChange(selectedMode);
    setIsOpen(false);
  };

  const handleCancel = () => {
    setSelectedMode(currentMode);
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="border-2 bg-white shadow-lg hover:bg-gray-50"
          >
            <Settings className="mr-2 h-4 w-4" />
            Mode: {getCurrentModeLabel()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium">Mode de correction</h4>
              <p className="text-xs text-muted-foreground">
                Choisissez le type de correction pour votre test
              </p>
            </div>

            <RadioGroup value={selectedMode} onValueChange={setSelectedMode}>
              {options.map((option) => (
                <div
                  key={option.value}
                  className="flex items-start space-x-3 space-y-0"
                >
                  <RadioGroupItem
                    value={option.value}
                    id={option.value}
                    className="mt-1"
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label
                      htmlFor={option.value}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      {option.description}
                    </p>
                  </div>
                </div>
              ))}
            </RadioGroup>

            <div className="flex justify-end space-x-2 pt-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                Annuler
              </Button>
              <Button
                size="sm"
                onClick={handleConfirm}
                disabled={!selectedMode}
              >
                <Check className="mr-1 h-4 w-4" />
                Confirmer
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
