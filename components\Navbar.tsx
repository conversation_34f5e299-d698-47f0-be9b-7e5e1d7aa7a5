'use client';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Menu } from 'lucide-react';
import { useSession } from 'next-auth/react';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from './ui/sheet';
import { ROUTES, TEST_URL_ON_TEST } from '@/config';
import { useTestState } from '@/context/test';
import { Button, buttonVariants } from './ui/button';
import { useTranslation } from '@/app/i18n/client';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select';
import { usei18nState } from '@/context/i18n';
import Notification from './Notification';
import CustomPopover from './CustomPopover';
import { PopoverClose } from '@radix-ui/react-popover';
import { Suspense, useRef } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { LeftWeb, LeftMobile } from './left-navbar-web';
import { NavigationMenuDemo } from './menu-nav';

import logo from '@/public/logo4.png';
import { SignOutBtn } from '@/components/sign-out-btn';

const Navbar = () => {
  const { lng, setLng } = usei18nState();
  const path = usePathname()!;
  const { isTart } = useTestState();
  const { t } = useTranslation(lng, 'navbar');
  const router = useRouter();
  const closeRef = useRef<HTMLButtonElement | null>(null);
  const closeSheet = useRef<HTMLButtonElement | null>(null); // ref to close sheet when click
  const { data: session } = useSession();

  return (
    <>
      {/* <Banner
        url="https://ifc-registration.vercel.app/"
        message={`Nous vous proposons du nouveau pour votre préparation, Pré-inscription Douala/Yaoundé`}
      /> */}
      <nav
        id="nav-bar"
        className={cn(
          `sticky inset-x-0 top-0 z-[40] h-16 max-w-[100vw] rounded-sm border-b border-gray-200 bg-white pb-3 pt-2 shadow-sm backdrop-blur-lg transition-all ${
            path.includes('auth') ||
            path.includes('signin') ||
            path.includes('signup') ||
            TEST_URL_ON_TEST.test(path)
              ? 'hidden'
              : ''
          }`,
        )}
      >
        <div className="relative mx-auto flex h-full flex-row-reverse items-center justify-evenly md:container md:flex-row lg:max-w-7xl">
          <Link
            href={`/`}
            className="z-1 flex h-full w-[160px] items-center justify-center !overflow-hidden sm:right-2 md:left-2"
          >
            <Image
              src={logo}
              width={160}
              height={80}
              alt="logo"
              className="bg-transparent"
            />
          </Link>
          <div className="z-50 hidden items-center justify-center gap-5 text-sm font-normal md:flex lg:text-lg">
            <NavigationMenuDemo />
          </div>
          <div className="z-50 hidden items-center justify-center gap-3 text-sm font-normal md:flex lg:text-lg">
            <Select
              defaultValue={lng}
              onValueChange={async (v) => {
                setLng(v as any);
              }}
            >
              <SelectTrigger
                islang
                className="!h-8 w-fit !gap-2 !rounded-l-full !rounded-r-full !p-3 text-gray-900"
              >
                <SelectValue placeholder="langue" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup className="">
                  <SelectLabel>Langue</SelectLabel>
                  <SelectItem className="flex" value="fr">
                    <div className="flex">
                      <Image
                        src={'/flag-fr.svg'}
                        loading="lazy"
                        alt="fr-flag"
                        width={25}
                        height={25}
                        className="mr-1"
                      />
                      FR-fr
                    </div>
                  </SelectItem>
                  <SelectItem className="flex" value="en">
                    <div className="flex">
                      <Image
                        src={'/flag-uk.svg'}
                        loading="lazy"
                        alt="fr-flag"
                        width={25}
                        height={25}
                        className="mr-1"
                      />
                      EN-uk
                    </div>
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
            <LeftWeb />
          </div>

          {/* mobile nav */}
          <div className="absolute left-3 md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button title="Menu mobile" aria-description="open menu">
                  <Menu className="h-9 w-9 text-white" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side={'left'}
                className="max-h-[100dvh] w-[290px] overflow-y-scroll"
              >
                <SheetHeader>
                  <SheetTitle>
                    <Link href={`/`} className="border">
                      <h2 className={`text-2xl font-normal`}>
                        Objectif Canada
                      </h2>
                      {/* <Image src={'/logo.png'} alt={'logo'} width={80} height={40} /> */}
                    </Link>
                  </SheetTitle>
                </SheetHeader>
                <hr className="py-3" />
                <div className="relative flex h-full w-full flex-col gap-3">
                  {session ? (
                    <Link
                      onClick={() => closeSheet.current?.click()}
                      href={`/dashboard/tcfhistory`}
                      className={cn(
                        `text-sm tracking-wider transition-all ${buttonVariants(
                          { variant: 'ghost' },
                        )}`,
                        {
                          'bg-accent font-semibold': path.includes('dashboard'),
                          'font-normal text-gray-900':
                            !path.includes('dashboard'),
                        },
                      )}
                      key={'dashboard'}
                    >
                      {t('Mon Compte')}
                    </Link>
                  ) : null}
                  {ROUTES.map((route) => {
                    if (route.path !== 'about') {
                      return (
                        <Button
                          variant={'ghost'}
                          onClick={() => {
                            router.push(`/${route.path}`);
                            closeSheet.current?.click();
                          }}
                          className={`text-sm tracking-wider transition-all ${
                            path.includes(route.path)
                              ? 'bg-accent font-semibold'
                              : 'font-normal text-gray-900'
                          }`}
                          key={route.path}
                        >
                          {t(route.value)}
                        </Button>
                      );
                    } else {
                      return (
                        <CustomPopover
                          offset={5}
                          key={route.path}
                          content={
                            <div className="flex flex-col gap-2 text-sm font-medium text-zinc-500">
                              <Link
                                onClick={() => {
                                  closeRef.current?.click();
                                  closeSheet.current?.click();
                                }}
                                className="hover:text-zinc-900"
                                href={'/about#whattcf'}
                              >
                                {"C'est quoi le TCF"}
                              </Link>
                              <Link
                                onClick={() => {
                                  closeRef.current?.click();
                                  closeSheet.current?.click();
                                }}
                                className="hover:text-zinc-900"
                                href={'/about#epreuves'}
                              >
                                {'Les epreuves'}
                              </Link>
                              <Link
                                onClick={() => {
                                  closeRef.current?.click();
                                  closeSheet.current?.click();
                                }}
                                className="hover:text-zinc-900"
                                href={'/about#registration'}
                              >
                                {'Inscription au TCF'}
                              </Link>
                              <Link
                                onClick={() => {
                                  closeRef.current?.click();
                                  closeSheet.current?.click();
                                }}
                                className="hover:text-zinc-900"
                                href={'/about#resultats'}
                              >
                                {'Resultat du TCF'}
                              </Link>
                              <Link
                                onClick={() => {
                                  closeRef.current?.click();
                                  closeSheet.current?.click();
                                }}
                                className="hover:text-zinc-900"
                                href={'/about#links'}
                              >
                                {'Liens utiles'}
                              </Link>
                              <PopoverClose ref={closeRef} />
                            </div>
                          }
                          trigger={
                            <Button
                              variant={'ghost'}
                              className={`text-sm tracking-wider transition-all ${
                                path.includes(route.path)
                                  ? 'bg-accent font-semibold'
                                  : 'font-normal text-gray-900'
                              }`}
                            >
                              {t(route.value)}
                            </Button>
                          }
                        />
                      );
                    }
                  })}

                  <LeftMobile />
                  <Select
                    defaultValue={lng}
                    onValueChange={async (v) => {
                      setLng(v as any);
                      closeSheet.current?.click();
                    }}
                  >
                    <SelectTrigger className="w-full p-2 px-1 text-gray-900">
                      <SelectValue placeholder="langue" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup className="">
                        <SelectLabel>Langue</SelectLabel>
                        <SelectItem
                          className="text-center tracking-wide"
                          value="fr"
                        >
                          Français
                        </SelectItem>
                        <SelectItem
                          className="text-center tracking-wide"
                          value="en"
                        >
                          Anglais
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <Suspense>
                    <SignOutBtn
                      handleClick={() => closeSheet.current?.click()}
                    />
                  </Suspense>
                </div>
              </SheetContent>
              <SheetClose ref={closeSheet} className="hidden" />
            </Sheet>
          </div>
          <div className="absolute right-2 md:hidden">
            {session ? (
              <Notification />
            ) : (
              <Button
                size={'sm'}
                className={cn('animate-buttonheartbeat text-xs')}
                onClick={() => router.push('/signin')}
              >
                Se connecter
              </Button>
            )}
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
